# Text Region Tool vs Panning Conflict Analysis

## Problem Analysis

After examining the codebase, I've identified the core issue with the text region tool conflicting with panning functionality. The problem lies in the event handling hierarchy and how different tools interact with mouse events.

## Current Implementation Issues

### 1. Event Capture Conflict in TextRegionDrawTool
- **File**: `/packages/frontend/components/editor/TextRegionDrawTool.tsx`
- **Issue**: Lines 181-191 create an invisible overlay that captures ALL mouse events when text-region tool is selected
- **Problem**: This overlay prevents stage drag events from reaching the CanvasWorkspace, blocking panning

### 2. Panning Logic Dependencies
- **File**: `/packages/frontend/components/editor/CanvasWorkspace.tsx`
- **Current Logic**: 
  - Panning works when: pan tool selected, modifier keys pressed, or dragging stage/background
  - Lines 195-209 in `handleStageDrag` check for these conditions
  - But TextRegionDrawTool's invisible overlay blocks these events

### 3. Tool State Management
- **File**: `/packages/frontend/components/editor/EditorContext.tsx`
- **Current Tools**: `'select' | 'text-region' | 'pan' | 'zoom'` (line 19)
- **Tool Selection**: Handled via LeftSidebar component

## Root Cause

The fundamental issue is that `TextRegionDrawTool` uses a full-canvas invisible overlay (`Rect` with `listening={state.selectedTool === 'text-region'}`) to capture mouse events. This overlay sits on top of everything and prevents the stage-level drag events that enable panning.

## Impact

1. **When text-region tool is selected**: 
   - Normal panning (with pan tool) doesn't work
   - Temporary panning (with Alt/Option key) doesn't work
   - Only way to pan is to switch to pan tool explicitly

2. **User Experience Issues**:
   - Breaks expected behavior of modifier-key panning
   - Forces users to constantly switch tools
   - Inconsistent interaction patterns

## Solution Strategy

The fix requires restructuring how the text region drawing tool captures events without blocking panning functionality. Key approaches:

1. **Conditional Event Handling**: Only capture events when actually needed for drawing
2. **Event Delegation**: Use stage-level event handlers with proper tool checks
3. **Modifier Key Respecting**: Allow panning when modifier keys are pressed, even during text-region tool usage

## Plan Tasks

1. **Remove Full-Canvas Overlay**: Replace the invisible overlay with stage-level event handling
2. **Integrate with Existing Event System**: Use the existing mouse event handlers in CanvasWorkspace
3. **Add Tool-Specific Logic**: Extend existing handlers to support text region drawing
4. **Preserve Panning Functionality**: Ensure all panning scenarios work regardless of selected tool
5. **Test Edge Cases**: Verify drawing still works correctly with the new approach

## Files to Modify

1. `/packages/frontend/components/editor/TextRegionDrawTool.tsx` - Main changes
2. `/packages/frontend/components/editor/CanvasWorkspace.tsx` - Event handler integration
3. Possibly `/packages/frontend/components/editor/EditorContext.tsx` - If state management changes needed

## Expected Outcome

- Text region tool works as expected
- Panning (both normal and temporary) works regardless of selected tool
- Consistent user experience across all tool interactions
- No breaking changes to existing functionality