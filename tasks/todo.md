# Fix Text Region Tool Drawing vs Panning Conflict

## Problem
When using the text region tool to draw a text region, it's not drawing but panning instead.

## Root Cause Analysis
The TextRegionDrawTool uses a full-canvas invisible overlay that captures all mouse events when the text-region tool is selected. This prevents stage-level drag events from reaching the CanvasWorkspace, blocking panning functionality.

## Plan

### Phase 1: Analysis and Understanding
- [x] Search and analyze text region tool implementation
- [x] Identify conflict between drawing and panning logic
- [x] Create detailed technical analysis

### Phase 2: Core Fix Implementation
- [ ] Remove the problematic full-canvas overlay from TextRegionDrawTool
- [ ] Integrate text region drawing logic into CanvasWorkspace event handlers
- [ ] Implement priority-based event handling (panning with modifier keys takes precedence)
- [ ] Update drawing state management in CanvasWorkspace

### Phase 3: Testing and Validation
- [ ] Test text region drawing functionality
- [ ] Test panning works regardless of selected tool
- [ ] Test temporary panning with Alt/Option key
- [ ] Verify no breaking changes to existing functionality

### Phase 4: Code Quality
- [ ] Run type checking with `tsc --noEmit`
- [ ] Ensure code follows existing patterns

## Files to Modify
1. `packages/frontend/components/editor/CanvasWorkspace.tsx` - Add text region drawing logic
2. `packages/frontend/components/editor/TextRegionDrawTool.tsx` - Remove overlay, simplify logic
3. `packages/frontend/components/editor/EditorContext.tsx` - Update state management if needed

## Expected Outcome
- Text region tool draws properly without interfering with panning
- Panning works regardless of selected tool
- Alt/Option + drag temporary panning works consistently
- No breaking changes to existing functionality