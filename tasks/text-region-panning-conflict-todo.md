# Text Region Tool vs Panning Conflict Fix - Implementation Plan

## Overview
Fix the conflict between text region drawing tool and panning functionality by restructuring event handling to avoid blocking stage-level drag events.

## Tasks

### 1. Remove Full-Canvas Overlay from TextRegionDrawTool
- [ ] **Task**: Remove the invisible overlay `Rect` that captures all mouse events
- **File**: `/packages/frontend/components/editor/TextRegionDrawTool.tsx`
- **Details**: 
  - Remove lines 181-191 (invisible overlay Rect)
  - Remove mouse event handlers from the overlay
  - Keep the drawing preview rectangle functionality

### 2. Integrate Text Region Drawing with CanvasWorkspace Events
- [ ] **Task**: Add text region drawing logic to existing mouse event handlers
- **File**: `/packages/frontend/components/editor/CanvasWorkspace.tsx`
- **Details**:
  - Extend `handleMouseDown` to support text region drawing initiation
  - Extend `handleStageDrag` to support text region drawing while respecting panning
  - Extend `handleMouseUp` to complete text region drawing
  - Ensure panning takes precedence when modifier keys are pressed

### 3. Move Drawing State to CanvasWorkspace
- [ ] **Task**: Centralize drawing state management in CanvasWorkspace
- **File**: `/packages/frontend/components/editor/CanvasWorkspace.tsx`
- **Details**:
  - Add drawing state to CanvasWorkspace component
  - Add drawing state management logic
  - Pass drawing state to TextRegionDrawTool as props

### 4. Refactor TextRegionDrawTool to be Stateless
- [ ] **Task**: Convert TextRegionDrawTool to receive drawing state as props
- **File**: `/packages/frontend/components/editor/TextRegionDrawTool.tsx`
- **Details**:
  - Remove internal drawing state
  - Receive drawing state and handlers as props
  - Focus only on rendering the preview rectangle

### 5. Add Priority Logic for Tool Interactions
- [ ] **Task**: Implement priority system for competing tool interactions
- **File**: `/packages/frontend/components/editor/CanvasWorkspace.tsx`
- **Details**:
  - Panning (with modifier keys) has highest priority
  - Text region drawing only when no modifier keys and text-region tool selected
  - Preserve existing select tool behavior

### 6. Test and Verify Functionality
- [ ] **Task**: Test all interaction scenarios
- **Details**:
  - Test text region drawing works correctly
  - Test panning works with pan tool
  - Test temporary panning works with Alt/Option key
  - Test no conflicts between tools
  - Test switching between tools works smoothly

## Technical Details

### Event Handling Priority Order:
1. **Modifier key panning** (Alt/Option + drag) - highest priority
2. **Pan tool** - when pan tool selected
3. **Text region drawing** - when text-region tool selected and no modifiers
4. **Region selection/interaction** - when select tool selected
5. **Stage clicks** - for deselection

### Key Implementation Points:
- Use `e.evt.preventDefault()` and `e.evt.stopPropagation()` carefully to control event flow
- Coordinate transformations must account for zoom and viewport offset
- Drawing state should be managed consistently with other canvas operations
- Preview rendering should be smooth and responsive

## Files to Modify:
1. `/packages/frontend/components/editor/TextRegionDrawTool.tsx` - Major refactor
2. `/packages/frontend/components/editor/CanvasWorkspace.tsx` - Event handler integration
3. No changes expected to EditorContext.tsx

## Expected Outcome:
- Text region tool works without blocking panning
- All existing panning functionality preserved
- Consistent user experience across all tools
- No breaking changes to other functionality