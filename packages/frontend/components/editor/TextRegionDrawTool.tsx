'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { Rect } from 'react-konva';
import { useEditor } from './EditorContext';
import { TextRegionResponse, TextRegionType, TranslationStatus } from '@/types/api';

interface DrawingState {
  isDrawing: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
}

export default function TextRegionDrawTool() {
  const { state, dispatch } = useEditor();
  const [drawingState, setDrawingState] = useState<DrawingState>({
    isDrawing: false,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
  });
  
  // Preview state for smooth drawing without frequent React updates
  const [previewDrawing, setPreviewDrawing] = useState<{
    currentX: number;
    currentY: number;
  } | null>(null);
  const rafId = useRef<number | null>(null);
  const latestCoords = useRef<{ x: number; y: number } | null>(null);

  // Throttled update using requestAnimationFrame for smooth drawing
  const updateDrawingPreview = useCallback(() => {
    if (latestCoords.current) {
      setPreviewDrawing({
        currentX: latestCoords.current.x,
        currentY: latestCoords.current.y
      });
      latestCoords.current = null;
    }
    rafId.current = null;
  }, []);

  // Handle mouse down to start drawing
  const handleMouseDown = useCallback((e: any) => {
    if (state.selectedTool !== 'text-region') return;

    const stage = e.target.getStage();
    const pointer = stage.getPointerPosition();
    
    // Transform pointer coordinates to account for zoom and viewport offset
    const transformedX = (pointer.x - state.viewportOffset.x) / state.zoom;
    const transformedY = (pointer.y - state.viewportOffset.y) / state.zoom;

    setDrawingState({
      isDrawing: true,
      startX: transformedX,
      startY: transformedY,
      currentX: transformedX,
      currentY: transformedY,
    });
    
    // Initialize preview drawing
    setPreviewDrawing({
      currentX: transformedX,
      currentY: transformedY
    });
  }, [state.selectedTool, state.zoom, state.viewportOffset]);

  // Handle mouse move while drawing with RAF throttling
  const handleMouseMove = useCallback((e: any) => {
    if (!drawingState.isDrawing || state.selectedTool !== 'text-region') return;

    const stage = e.target.getStage();
    const pointer = stage.getPointerPosition();
    
    // Transform pointer coordinates to account for zoom and viewport offset
    const transformedX = (pointer.x - state.viewportOffset.x) / state.zoom;
    const transformedY = (pointer.y - state.viewportOffset.y) / state.zoom;

    // Store latest coordinates for RAF update
    latestCoords.current = { x: transformedX, y: transformedY };
    
    // Schedule RAF update if not already scheduled
    if (rafId.current === null) {
      rafId.current = requestAnimationFrame(updateDrawingPreview);
    }
  }, [drawingState.isDrawing, state.selectedTool, state.zoom, state.viewportOffset, updateDrawingPreview]);

  // Handle mouse up to finish drawing
  const handleMouseUp = useCallback(() => {
    if (!drawingState.isDrawing || state.selectedTool !== 'text-region') return;

    // Cancel any pending RAF updates
    if (rafId.current !== null) {
      cancelAnimationFrame(rafId.current);
      rafId.current = null;
    }

    // Use preview coordinates if available, otherwise use drawing state
    const finalX = previewDrawing?.currentX ?? drawingState.currentX;
    const finalY = previewDrawing?.currentY ?? drawingState.currentY;
    
    const width = Math.abs(finalX - drawingState.startX);
    const height = Math.abs(finalY - drawingState.startY);

    // Only create region if it's large enough
    if (width > 10 && height > 10) {
      const x = Math.min(drawingState.startX, finalX);
      const y = Math.min(drawingState.startY, finalY);

      // Create new text region
      const newRegion: TextRegionResponse = {
        id: `temp-${Date.now()}`, // Temporary ID
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        page_id: state.currentPage?.id || '',
        region_type: TextRegionType.SPEECH_BUBBLE,
        x,
        y,
        width,
        height,
        original_text: '',
        confidence_score: 1.0,
        translated_text: '',
        translation_status: TranslationStatus.PENDING,
        font_family: 'Arial',
        font_size: 14,
        font_color: '#000000',
        background_color: 'transparent',
      };

      dispatch({ type: 'ADD_TEXT_REGION', payload: newRegion });
      dispatch({ type: 'SET_SELECTED_REGIONS', payload: [newRegion.id] });
      // Auto-switch to select mode after creating a text region
      dispatch({ type: 'SET_SELECTED_TOOL', payload: 'select' });
      
      // Auto-focus the newly created region for editing after a small delay
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('auto-edit-region', { detail: { regionId: newRegion.id } }));
      }, 100);
    }

    // Clean up drawing state and preview
    setDrawingState({
      isDrawing: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
    });
    setPreviewDrawing(null);
    latestCoords.current = null;
  }, [drawingState, previewDrawing, state.selectedTool, state.currentPage, dispatch]);

  // Cleanup RAF on unmount
  useEffect(() => {
    return () => {
      if (rafId.current !== null) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, []);

  // Calculate drawing rectangle dimensions using preview coordinates when available
  const currentX = previewDrawing?.currentX ?? drawingState.currentX;
  const currentY = previewDrawing?.currentY ?? drawingState.currentY;
  
  const drawingRect = {
    x: Math.min(drawingState.startX, currentX),
    y: Math.min(drawingState.startY, currentY),
    width: Math.abs(currentX - drawingState.startX),
    height: Math.abs(currentY - drawingState.startY),
  };

  return (
    <>
      {/* Invisible overlay to capture mouse events */}
      <Rect
        x={0}
        y={0}
        width={state.canvasSize.width}
        height={state.canvasSize.height}
        fill="transparent"
        listening={state.selectedTool === 'text-region'}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      />

      {/* Drawing preview rectangle */}
      {drawingState.isDrawing && drawingRect.width > 0 && drawingRect.height > 0 && (
        <Rect
          x={drawingRect.x}
          y={drawingRect.y}
          width={drawingRect.width}
          height={drawingRect.height}
          fill="rgba(59, 130, 246, 0.2)"
          stroke="#3b82f6"
          strokeWidth={2}
          dash={[5, 5]}
          listening={false}
        />
      )}
    </>
  );
}
